import 'package:khelnet/utils/API/api_model.dart';

import '../constants/config.dart';


String baseUrl = CONFIG().baseUrl;
//  String baseUrl = 'https://backendv2.khelnet.in/api/v1/';

class APIConstant {
  static final Auth auth = Auth();
  static final OnBoard onBoard = OnBoard();
  static final MyAcademy myAcademy = MyAcademy();
  static final Attendance attendance = Attendance();
  static final Fees fees = Fees();
  static final Report report = Report();
  static final FeesReinder feesReinder = FeesReinder();
  static final CoachAttendance coachAttendance = CoachAttendance();
  static final Expense expense = Expense();
  static final Wallet wallet = Wallet();
  static final Assessment assessment = Assessment();
  static final Billing billing = Billing();
  static final AcademyProfile academyProfile = AcademyProfile();
  static final Admission admission = Admission();
  static final CRMAndEnquiry crmAndEnquiry = CRMAndEnquiry();
}

class Auth {
  final ApiModel appConfig =
       ApiModel(endpoint: "${baseUrl}appConfig", type: APIType.get);
  final ApiModel getMe =
       ApiModel(endpoint: "${baseUrl}academy/getMe", type: APIType.get);
  final ApiModel updateMe =  ApiModel(
      endpoint: "${baseUrl}academy/updateMe", type: APIType.patch);
  final ApiModel checkUer =
       ApiModel(endpoint: "${baseUrl}auth/checkUser", type: APIType.post);
  final ApiModel signUp =
       ApiModel(endpoint: "${baseUrl}auth/signup", type: APIType.post);
  final ApiModel signUpVerification =  ApiModel(
      endpoint: "${baseUrl}auth/signupVerify", type: APIType.post);
  final ApiModel otpVerification =  ApiModel(
      endpoint: "${baseUrl}auth/otpVerification", type: APIType.post);
  final ApiModel resendOTP =
       ApiModel(endpoint: "${baseUrl}auth/resendOTP", type: APIType.post);
  final ApiModel deleteMe =  ApiModel(
      endpoint: "${baseUrl}academy/deleteMe", type: APIType.delete);
}

class OnBoard {
  final ApiModel uploadImage =  ApiModel(
      endpoint: "${baseUrl}utils/uploadImage", type: APIType.post);
  final ApiModel addCenterBatch =  ApiModel(
      endpoint: "${baseUrl}academyCenter/bulkCreateCentersAndBatches",
      type: APIType.post);
  final ApiModel addCoaches =
       ApiModel(endpoint: "${baseUrl}coach", type: APIType.post);
  final ApiModel getAcademyCenter =  ApiModel(
      endpoint: "${baseUrl}academyCenter/getAcademyCenter", type: APIType.get);
  final ApiModel getAcademyBatches =  ApiModel(
      endpoint: "${baseUrl}academyCenterBatch/getAcademyCenterBatch/",
      type: APIType.get);
  final ApiModel addCharges =
       ApiModel(endpoint: "${baseUrl}charge", type: APIType.post);
  final ApiModel addPlans =
       ApiModel(endpoint: "${baseUrl}plan", type: APIType.post);
  final ApiModel getCharges =
       ApiModel(endpoint: "${baseUrl}charge", type: APIType.get);
  final ApiModel getPlans =
       ApiModel(endpoint: "${baseUrl}plan", type: APIType.get);
  final ApiModel addStudent =
       ApiModel(endpoint: "${baseUrl}student", type: APIType.post);
}

class MyAcademy {
  final ApiModel getMyAcademy =  ApiModel(
      endpoint: "${baseUrl}academy/getMyAcademy", type: APIType.get);
  final ApiModel getCenters =
       ApiModel(endpoint: "${baseUrl}academyCenter", type: APIType.get);
  final ApiModel getCoaches =
       ApiModel(endpoint: "${baseUrl}coach", type: APIType.get);
  final ApiModel getPlans =
       ApiModel(endpoint: "${baseUrl}plan", type: APIType.get);
  final ApiModel getCharges =
       ApiModel(endpoint: "${baseUrl}charge", type: APIType.get);
  final ApiModel getStudent =
       ApiModel(endpoint: "${baseUrl}student", type: APIType.get);
  final ApiModel getStudentPlanInstallmentTransaction =  ApiModel(
      endpoint:
          "${baseUrl}studentFeeTransaction/getStudentPlanInstallmentTransaction",
      type: APIType.get);
  final ApiModel getUserNotifications =  ApiModel(
      endpoint: "${baseUrl}userNotification", type: APIType.get);
  final ApiModel deleteUserNotification =  ApiModel(
      endpoint: "${baseUrl}userNotification", type: APIType.delete);
  final ApiModel addBatch =  ApiModel(
      endpoint: "${baseUrl}academyCenterBatch", type: APIType.post);
  final ApiModel assignCenterBatch =  ApiModel(
      endpoint: "${baseUrl}coachAssignBatch", type: APIType.post);
}

class Attendance {
  final ApiModel studentsForAttendance =  ApiModel(
      endpoint: "${baseUrl}student/studentForAttendance", type: APIType.get);
  final ApiModel saveAttendance =  ApiModel(
      endpoint: "${baseUrl}studentAttendance", type: APIType.post);
  final ApiModel updateAttendance =  ApiModel(
      endpoint: "${baseUrl}studentAttendance", type: APIType.patch);
  final ApiModel unmarkedStudents =  ApiModel(
      endpoint: "${baseUrl}studentAttendance", type: APIType.get);
  final ApiModel studentAnalytics =  ApiModel(
      endpoint: "${baseUrl}studentAttendance/analytics", type: APIType.get);
}

class Fees {
  final ApiModel academyTax =
       ApiModel(endpoint: "${baseUrl}academyTax", type: APIType.get);
  final ApiModel addAcademyTax =
       ApiModel(endpoint: "${baseUrl}academyTax", type: APIType.post);
  final ApiModel getStudentsForFees =  ApiModel(
      endpoint: "${baseUrl}student/getStudentForFee", type: APIType.get);
  final ApiModel studentFeeTransaction =  ApiModel(
      endpoint: "${baseUrl}studentFeeTransaction", type: APIType.get);
  final ApiModel bulkRenew =  ApiModel(
      endpoint: "${baseUrl}studentFeeTransaction/bulkRenew",
      type: APIType.post);
  final ApiModel bulkInstallmentRenew =  ApiModel(
      endpoint: "${baseUrl}studentFeeTransaction/bulkUpdate",
      type: APIType.patch);
  final ApiModel renew =  ApiModel(
      endpoint: "${baseUrl}studentFeeTransaction/renew", type: APIType.post);
  final ApiModel makePayment =  ApiModel(
      endpoint: "${baseUrl}studentFeeTransaction", type: APIType.post);
  final ApiModel availableEndPoints =  ApiModel(
      endpoint:
          "${baseUrl}studentFeeTransaction/getInstallmentAndPasDueAvailable",
      type: APIType.get);
  final ApiModel getFeeDashboard =  ApiModel(
      endpoint: "${baseUrl}studentFeeTransaction/getFeeDashboard",
      type: APIType.get);
  final ApiModel getFeeCenterBatch =  ApiModel(
      endpoint: "${baseUrl}studentFeeTransaction/getCenterOrBatchWiseRevenue",
      type: APIType.get);
  final ApiModel cashFlow =  ApiModel(
      endpoint: "${baseUrl}studentFeeTransaction/getCashFlow",
      type: APIType.get);
}

class Report {
  final ApiModel methodAnalytics =  ApiModel(
      endpoint: "${baseUrl}studentFeeTransaction/transactionMethodAnalytics",
      type: APIType.get);
  final ApiModel collectionAnalytics =  ApiModel(
      endpoint:
          "${baseUrl}studentFeeTransaction/transactionCollectionAnalytics",
      type: APIType.get);
}

class FeesReinder {
  final ApiModel feesReminder =  ApiModel(
      endpoint: "${baseUrl}studentFeeReminder", type: APIType.post);
  final ApiModel getReminders =  ApiModel(
      endpoint: "${baseUrl}studentFeeReminder", type: APIType.get);
  final ApiModel instantReminder =  ApiModel(
      endpoint: "${baseUrl}studentFeeReminder/instantSendReminder",
      type: APIType.post);
  final ApiModel getReminderSetting =  ApiModel(
      endpoint: "${baseUrl}academyFeeReminderSetting", type: APIType.get);
  final ApiModel updateReminderSetting =  ApiModel(
      endpoint: "${baseUrl}academyFeeReminderSetting", type: APIType.patch);
  final ApiModel addReminderSetting =  ApiModel(
      endpoint: "${baseUrl}academyFeeReminderSetting", type: APIType.post);
  final ApiModel todaySentReminderCount =  ApiModel(
      endpoint: "${baseUrl}studentFeeReminderSentCount/todaySentReminderCount",
      type: APIType.get);
  final ApiModel studentFeeReminderSentCount =  ApiModel(
      endpoint: "${baseUrl}studentFeeReminderSentCount", type: APIType.get);
}

class CoachAttendance {
  final ApiModel coachAttendance =
       ApiModel(endpoint: "${baseUrl}coachAttendance", type: APIType.get);
  final ApiModel coachLogin =
       ApiModel(endpoint: "${baseUrl}auth/coachLogin", type: APIType.post);
  final ApiModel lastAction =  ApiModel(
      endpoint: "${baseUrl}coachAttendance/lastAction", type: APIType.get);
  final ApiModel clockIn =  ApiModel(
      endpoint: "${baseUrl}coachAttendance/checkIn", type: APIType.post);
  final ApiModel clockOut =  ApiModel(
      endpoint: "${baseUrl}coachAttendance/checkOut", type: APIType.post);
  final ApiModel coachGetMe =
       ApiModel(endpoint: "${baseUrl}coach/getMe", type: APIType.get);
  final ApiModel coachUpdateMe =
       ApiModel(endpoint: "${baseUrl}coach/updateMe", type: APIType.patch);
}

class Expense {
  final ApiModel getCategory =
       ApiModel(endpoint: "${baseUrl}expenseCategory", type: APIType.get);
  final ApiModel academyExpense =
       ApiModel(endpoint: "${baseUrl}academyExpense", type: APIType.get);
  final ApiModel addCategory =
       ApiModel(endpoint: "${baseUrl}expenseCategory", type: APIType.post);
  final ApiModel addExpense =
       ApiModel(endpoint: "${baseUrl}academyExpense", type: APIType.post);
  final ApiModel addCoachSalary =  ApiModel(
      endpoint: "${baseUrl}academyExpense/addCoachSalary", type: APIType.post);
}

class Wallet {
  final ApiModel academyCashFreeCred =  ApiModel(
      endpoint: "${baseUrl}academyCashFreeCred", type: APIType.get);
  final ApiModel cashFree =
       ApiModel(endpoint: "${baseUrl}cashfree", type: APIType.get);
  final ApiModel getWalletTransactionOverview =  ApiModel(
      endpoint: "${baseUrl}studentFeeTransaction/getWalletTransactionOverview",
      type: APIType.get);
}

class Assessment {
  final ApiModel addAssessmentTemplate =  ApiModel(
      endpoint: "${baseUrl}assessmentTemplate", type: APIType.post);
  final ApiModel getAllTemplates =  ApiModel(
      endpoint: "${baseUrl}assessmentTemplate", type: APIType.get);
  final ApiModel studentAssessmentDetail =  ApiModel(
      endpoint: "${baseUrl}studentAssessmentDetail", type: APIType.post);
  final ApiModel getStudentAssessmentDetail =  ApiModel(
      endpoint: "${baseUrl}studentAssessmentDetail", type: APIType.get);
  final ApiModel lastPosition =  ApiModel(
      endpoint: "${baseUrl}assessmentTemplate/lastPosition", type: APIType.get);
  final ApiModel studentLevelOverview =  ApiModel(
      endpoint: "${baseUrl}studentAssessmentTransaction/studentLevelOverview",
      type: APIType.get);
  final ApiModel academyAssessmentSetting =  ApiModel(
      endpoint: "${baseUrl}academyAssessmentSetting", type: APIType.get);
  final ApiModel updateAcademyAssessmentSetting =  ApiModel(
      endpoint: "${baseUrl}academyAssessmentSetting", type: APIType.patch);
  final ApiModel studentsWithNextTemplate =  ApiModel(
      endpoint: "${baseUrl}student/studentsWithNextTemplate",
      type: APIType.get);
  final ApiModel currentMonthOverview =  ApiModel(
      endpoint: "${baseUrl}studentAssessmentTransaction/currentMonthOverview",
      type: APIType.get);
  final ApiModel lastTwelveMonth =  ApiModel(
      endpoint:
          "${baseUrl}studentAssessmentTransaction/lastTwelveMonthsOverview",
      type: APIType.get);
  final ApiModel lateAssessmentStudent =  ApiModel(
      endpoint: "${baseUrl}studentAssessmentTransaction/lateAssessmentStudent",
      type: APIType.get);
  final ApiModel lateAssessmentCoach =  ApiModel(
      endpoint: "${baseUrl}studentAssessmentTransaction/coachAssessmentCounts",
      type: APIType.get);
  final ApiModel studentAssessment =  ApiModel(
      endpoint: "${baseUrl}studentAssessmentDetail", type: APIType.get);
  final ApiModel upcomingStudentAssessment =  ApiModel(
      endpoint:
          "${baseUrl}studentAssessmentTransaction/upcomingAssessmentsStudent",
      type: APIType.get);
  final ApiModel pendingStudentAssessment =  ApiModel(
      endpoint: "${baseUrl}studentAssessmentTransaction", type: APIType.get);
  final ApiModel studentAssessmentDetailOverview =  ApiModel(
      endpoint: "${baseUrl}studentAssessmentDetail/overview",
      type: APIType.get);
  final ApiModel studentAssessmentTransactionOverview =  ApiModel(
      endpoint: "${baseUrl}studentAssessmentTransaction/overview",
      type: APIType.get);
}

class Billing {
  final ApiModel subscriptionTransaction =  ApiModel(
      endpoint: "${baseUrl}subscriptionTransaction", type: APIType.get);
  final ApiModel academySubscribedPlan =  ApiModel(
      endpoint: "${baseUrl}academySubscribedPlan", type: APIType.get);
  final ApiModel subscriptionPlan =
       ApiModel(endpoint: "${baseUrl}subscriptionPlan", type: APIType.get);
  final ApiModel activeFreeTrail =  ApiModel(
      endpoint: "${baseUrl}subscriptionPlan/ActiveFreeTrail",
      type: APIType.post);
  final ApiModel subscribe =  ApiModel(
      endpoint: "${baseUrl}subscriptionPlan/subscribe", type: APIType.post);
}

class AcademyProfile {
  final ApiModel academyProfile =
       ApiModel(endpoint: "${baseUrl}academyProfile", type: APIType.get);
  final ApiModel addAcademyProfile =
       ApiModel(endpoint: "${baseUrl}academyProfile", type: APIType.post);
  final ApiModel uploadMultipleImage =  ApiModel(
      endpoint: "${baseUrl}utils/uploadMultipleImage", type: APIType.post);
}

class Admission {
  final ApiModel admissionParameter =  ApiModel(
      endpoint: "${baseUrl}admissionParameter", type: APIType.get);
  final ApiModel academyAdmissionParameter =  ApiModel(
      endpoint: "${baseUrl}academyAdmissionParameter", type: APIType.post);
  final ApiModel studentAdmissionDetail =  ApiModel(
      endpoint: "${baseUrl}studentAdmissionDetail", type: APIType.get);
  final ApiModel getAddedAdmissionParam =  ApiModel(
      endpoint: "${baseUrl}academyAdmissionParameter", type: APIType.get);
}

class CRMAndEnquiry {
  final ApiModel enquiry =
       ApiModel(endpoint: "${baseUrl}enquiry", type: APIType.get);
  final ApiModel enquiryComment =
       ApiModel(endpoint: "${baseUrl}enquiryComment", type: APIType.post);
  final ApiModel addEnquiry =
       ApiModel(endpoint: "${baseUrl}enquiry", type: APIType.post);
}
