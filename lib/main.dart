import 'package:bot_toast/bot_toast.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get_navigation/src/root/get_material_app.dart';
import 'package:khelnet/firebase_options.dart';
import 'package:khelnet/global/constants/size.dart';
import 'package:khelnet/modules/splash/controller/network_controller.dart';
import 'package:khelnet/utils/constants/color_constant.dart';
import 'package:khelnet/utils/constants/config.dart';
import 'package:khelnet/utils/constants/routes_constant.dart';
import 'package:khelnet/utils/manager/navigation_manager.dart';
import 'package:khelnet/utils/manager/storage_manager.dart';
import 'package:permission_handler/permission_handler.dart';

Future<void> main() async {
  CONFIG().config(Environment.development);
  WidgetsFlutterBinding.ensureInitialized();
  getStatusBarColor(color: ColorConstant.transparent);
  await Permission.notification.isDenied.then((value) {
    if (value) {
      Permission.notification.request();
    }
  });
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await StorageManager.instance.setSPInstance();
  runApp(const MyApp());
  DependencyInjection.init();
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    MySize.init(context);
    return GetMaterialApp(
      builder: BotToastInit(),
      debugShowCheckedModeBanner: false,
      navigatorObservers: [BotToastNavigatorObserver()],
      onGenerateRoute: NavigationManager.onGenerateRoute,
      navigatorKey: NavigationManager.navigatorKey,
      title: 'KhelNet',
      theme: ThemeData(
          scaffoldBackgroundColor: ColorConstant.white, useMaterial3: false),
      initialRoute: RouteConstant.splashScreen,
    );
  }
}

getStatusBarColor({required Color color, Brightness? statusBarIconBrightness}) {
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
    statusBarColor: color, // Change this to the desired color
    statusBarIconBrightness: statusBarIconBrightness ?? Brightness.dark,
  ));
}
