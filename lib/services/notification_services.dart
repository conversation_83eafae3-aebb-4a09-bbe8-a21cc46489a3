import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:khelnet/global/constants/global_methods.dart';

class NotificationService {
  FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  AndroidNotificationChannel channel = const AndroidNotificationChannel(
    'high_importance_channel',
    'High Importance Notifications',
    description: 'This channel is used for important notifications.',
    importance: Importance.max,
  );

  bool _initialized = false;

  Future<void> init() async {
    if (!_initialized) {
      await _initializeLocalNotifications();
      await _initializeFCM();
      _initialized = true;
    }
  }

  Future<void> _initializeLocalNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings("@mipmap/ic_launcher");

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestSoundPermission: true,
      requestBadgePermission: true,
      requestAlertPermission: true,
      defaultPresentAlert: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await flutterLocalNotificationsPlugin.initialize(initializationSettings,
        onDidReceiveNotificationResponse: selectNotification);

    if (Platform.isAndroid) {
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);
    }

    if (Platform.isIOS) {
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.initialize(initializationSettingsIOS);
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
    }
  }

  Future<void> _initializeFCM() async {
    if (Platform.isIOS) {
      await FirebaseMessaging.instance.requestPermission();
    }

    FirebaseMessaging.instance
        .getInitialMessage()
        .then((RemoteMessage? message) {
      if (message != null) {
        Future.delayed(const Duration(seconds: 1), () {
          GlobalMethods().notificationRoute(message.data["click_action"] ?? "");
          // var onTap = CommonFunction.getNotificationOnTap(message.data);
          // onTap();
          _handleMessage(message);
        });
        //_handleMessage(message);
      }
    });

    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      log('message ===> ${message.data}');
      // if (Get.currentRoute != Routes.NOTIFICATION) {
      //   isNewNotification.value = true;
      // }
      if(Platform.isAndroid) {
        _showNotification(message);
      }
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage? message) {
      if (message != null) {
        Future.delayed(const Duration(seconds: 1), () {
          log("Handling onMessageOpenedApp: ${message.data}");
          _handleMessage(message);
        });
      }
    });
  }

  Future<void> _showNotification(RemoteMessage message) async {
    log("Notification Data ${message.data}");
    final NotificationDetails notificationDetails = NotificationDetails(
      android: AndroidNotificationDetails(
        channel.id,
        channel.name,
        channelDescription: channel.description,
        icon: '@mipmap/ic_launcher',
      ),
    );

    await flutterLocalNotificationsPlugin.show(
      0,
      message.notification?.title,
      message.notification?.body,
      notificationDetails,
      payload: jsonEncode(message.data),
    );
  }

  Future<void> selectNotification(
      NotificationResponse? notificationResponse) async {
    if (notificationResponse?.payload != null) {
      Future.delayed(const Duration(seconds: 1), () {
        var payload = jsonDecode(notificationResponse?.payload ?? "");
        // notificationResponse?.payload?.split(": ").last.replaceAll("}", "");
        log("Payload $payload");
        GlobalMethods().notificationRoute(payload['click_action'] ?? "");
      });
      log("Notification clicked with payload: ${notificationResponse?.payload}");
      // Implement navigation or other actions based on the payload
    }
  }

  Future<void> _handleMessage(RemoteMessage message) async {
    log("Message data: ${message.data}");
  }
}
