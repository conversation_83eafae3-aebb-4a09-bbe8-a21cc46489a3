import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:khelnet/common_widgets/custom_app_bar.dart';
import 'package:khelnet/common_widgets/custom_svg_picture.dart';
import 'package:khelnet/common_widgets/toast/toast_utils.dart';
import 'package:khelnet/global/constants/size.dart';
import 'package:khelnet/modules/private_route/my_academy/controller/notification_controller/notification_bloc.dart';
import 'package:khelnet/modules/private_route/my_academy/controller/notification_controller/notification_event.dart';
import 'package:khelnet/modules/private_route/my_academy/controller/notification_controller/notification_state.dart';
import 'package:khelnet/modules/private_route/my_academy/model/notification_model.dart';
import 'package:khelnet/utils/constants/app_asset.dart';
import 'package:khelnet/utils/constants/color_constant.dart';
import 'package:khelnet/utils/theme/typography.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  late NotificationBloc _notificationBloc;
  List<NotificationItem> notifications = [];
  int currentPage = 1;
  final int limit = 20;
  bool isLoading = false;
  bool hasReachedMax = false;

  @override
  void initState() {
    super.initState();
    _notificationBloc = BlocProvider.of<NotificationBloc>(context);
    _loadNotifications();
  }

  void _loadNotifications({bool isRefresh = false}) {
    if (isRefresh) {
      currentPage = 1;
      notifications.clear();
      hasReachedMax = false;
    }

    if (!isLoading && !hasReachedMax) {
      _notificationBloc.add(GetNotifications(page: currentPage, limit: limit));
    }
  }

  void _deleteNotification(int notificationId, int index) {
    _notificationBloc.add(DeleteNotification(notificationId: notificationId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar("Notifications"),
      body: BlocListener<NotificationBloc, NotificationState>(
        listener: (context, state) {
          if (state is NotificationLoading) {
            if (currentPage == 1) {
              setState(() {
                isLoading = true;
              });
            }
          } else if (state is NotificationSuccess) {
            setState(() {
              isLoading = false;
              if (currentPage == 1) {
                notifications = state.notificationModel.data.rows;
              } else {
                notifications.addAll(state.notificationModel.data.rows);
              }

              if (state.notificationModel.data.rows.length < limit) {
                hasReachedMax = true;
              } else {
                currentPage++;
              }
            });
          } else if (state is NotificationFailure) {
            setState(() {
              isLoading = false;
            });
            ToastUtils.showFailed(message: state.msg);
          } else if (state is DeleteNotificationSuccess) {
            ToastUtils.showSuccess(message: state.msg);
            _loadNotifications(isRefresh: true);
          } else if (state is DeleteNotificationFailure) {
            ToastUtils.showFailed(message: state.msg);
          }
        },
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (isLoading && notifications.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (notifications.isEmpty && !isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CustomSvgPicture(
              AppAsset.noStudentFound,
              height: 200,
              width: 200,
            ),
            Gap(MySize.getScaledSizeHeight(20)),
            TypoGraphy.text(
              "No notifications yet",
              level: 3,
              color: ColorConstant.primaryColor,
            ),
            Gap(MySize.getScaledSizeHeight(10)),
            TypoGraphy.text(
              "All your notifications will be shown here",
              level: 1,
              color: Colors.grey,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _loadNotifications(isRefresh: true);
      },
      child: ListView.separated(
        padding: EdgeInsets.all(MySize.getScaledSizeHeight(16)),
        itemCount: notifications.length + (hasReachedMax ? 0 : 1),
        separatorBuilder: (context, index) => Gap(MySize.getScaledSizeHeight(12)),
        itemBuilder: (context, index) {
          if (index >= notifications.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final notification = notifications[index];
          return _buildNotificationItem(notification, index);
        },
      ),
    );
  }

  Widget _buildNotificationItem(NotificationItem notification, int index) {
    return Dismissible(
      key: Key(notification.id.toString()),
      direction: DismissDirection.endToStart,
      confirmDismiss: (direction) async {
        return await _showDeleteConfirmation(notification.id, index);
      },
      background: Container(
        alignment: Alignment.centerRight,
        padding: EdgeInsets.only(right: MySize.getScaledSizeWidth(20)),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(MySize.getScaledSizeHeight(12)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.delete,
              color: Colors.white,
              size: 30,
            ),
            Gap(MySize.getScaledSizeHeight(4)),
            TypoGraphy.text(
              "Delete",
              color: Colors.white,
              level: 1,
              fontWeight: FontWeight.w600,
            ),
          ],
        ),
      ),
      child: Container(
        padding: EdgeInsets.all(MySize.getScaledSizeHeight(16)),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(MySize.getScaledSizeHeight(12)),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: MySize.getScaledSizeWidth(8),
                  height: MySize.getScaledSizeHeight(8),
                  decoration: const BoxDecoration(
                    color: ColorConstant.primaryColor,
                    shape: BoxShape.circle,
                  ),
                ),
                Gap(MySize.getScaledSizeWidth(12)),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TypoGraphy.text(
                        notification.title,
                        level: 2,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                        textAlign: TextAlign.start,
                        maxLines: 2,
                        textOverFlow: TextOverflow.ellipsis,
                      ),
                      Gap(MySize.getScaledSizeHeight(8)),
                      TypoGraphy.text(
                        notification.body,
                        level: 1,
                        color: Colors.grey[600],
                        textAlign: TextAlign.start,
                        maxLines: 3,
                        textOverFlow: TextOverflow.ellipsis,
                      ),
                      Gap(MySize.getScaledSizeHeight(12)),
                      TypoGraphy.text(
                        _formatDate(notification.createdAt),
                        level: 1,
                        color: Colors.grey[500],
                        textAlign: TextAlign.start,
                        fontSize: MySize.getScaledSizeHeight(12),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<bool?> _showDeleteConfirmation(int notificationId, int index) async {
    return await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: TypoGraphy.text(
            "Delete Notification",
            level: 3,
            fontWeight: FontWeight.w600,
          ),
          content: TypoGraphy.text(
            "Are you sure you want to delete this notification?",
            level: 1,
            textAlign: TextAlign.start,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: TypoGraphy.text(
                "Cancel",
                level: 1,
                color: Colors.grey,
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(true);
                _deleteNotification(notificationId, index);
              },
              child: TypoGraphy.text(
                "Delete",
                level: 1,
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        );
      },
    );
  }

  String _formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return "1 day ago";
      } else if (difference.inDays < 7) {
        return "${difference.inDays} days ago";
      } else {
        return DateFormat('MMM dd, yyyy').format(dateTime);
      }
    } else if (difference.inHours > 0) {
      return "${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago";
    } else if (difference.inMinutes > 0) {
      return "${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago";
    } else {
      return "Just now";
    }
  }
}
