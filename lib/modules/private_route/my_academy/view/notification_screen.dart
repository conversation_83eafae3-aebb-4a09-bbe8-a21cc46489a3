import 'package:flutter/material.dart';
import 'package:khelnet/global/constants/gradient_text.dart';
import 'package:khelnet/utils/constants/color_constant.dart';
import 'package:khelnet/utils/theme/typography.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  @override
  Widget build(BuildContext context) {
    return  Center(
      child: GradientText(gradient: const LinearGradient(colors: [
        ColorConstant.white,
        ColorConstant.primaryColor,
      ]),
      child: TypoGraphy.text("All your notifications will be shown here",color: ColorConstant.primaryColor,level: 2)),
    );
  }
}
