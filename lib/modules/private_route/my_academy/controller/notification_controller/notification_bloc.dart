import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:khelnet/modules/private_route/my_academy/controller/notification_controller/notification_event.dart';
import 'package:khelnet/modules/private_route/my_academy/controller/notification_controller/notification_state.dart';
import 'package:khelnet/modules/private_route/my_academy/model/notification_model.dart';

import '../../../../../services/network_client.dart';
import '../../../../../utils/API/api_constant.dart';
import '../../../../../utils/API/api_model.dart';
import '../../../../auth/login/model/academy_login_model.dart';

class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  NotificationBloc() : super(NotificationInitial()) {
    on<GetNotifications>(_getNotifications);
    on<DeleteNotification>(_deleteNotification);
  }

  _getNotifications(GetNotifications event, Emitter<NotificationState> emit) async {
    try {
      emit(NotificationLoading());
      final Map<String, dynamic> params = {
        'page': event.page,
        'limit': event.limit,
      };
      
      await NetworkClient.getInstance.callApi(
        apiModel: APIConstant.myAcademy.getUserNotifications,
        params: params,
        successCallback: (response, message) async {
          NotificationModel notificationModel = NotificationModel.fromJson(response);
          emit(NotificationSuccess(notificationModel: notificationModel));
        },
        failureCallback: (response, statusCode) {
          ErrorModel errorModel = ErrorModel.fromJson(response);
          emit(NotificationFailure(msg: errorModel.message));
        },
      );
    } catch (e) {
      log(e.toString());
      emit(NotificationFailure(msg: "Something went wrong"));
    }
  }

  _deleteNotification(DeleteNotification event, Emitter<NotificationState> emit) async {
    try {
      emit(DeleteNotificationLoading());

      final deleteApiModel = ApiModel(
        endpoint: "${APIConstant.myAcademy.deleteUserNotification.endpoint}/${event.notificationId}",
        type: APIType.delete,
      );

      await NetworkClient.getInstance.callApi(
        apiModel: deleteApiModel,
        params: {},
        successCallback: (response, message) async {
          ErrorModel errorModel = ErrorModel.fromJson(response);
          emit(DeleteNotificationSuccess(msg: errorModel.message));
        },
        failureCallback: (response, statusCode) {
          ErrorModel errorModel = ErrorModel.fromJson(response);
          emit(DeleteNotificationFailure(msg: errorModel.message));
        },
      );
    } catch (e) {
      log(e.toString());
      emit(DeleteNotificationFailure(msg: "Something went wrong"));
    }
  }
}
