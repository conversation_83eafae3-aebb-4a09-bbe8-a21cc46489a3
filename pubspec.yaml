name: khelnet
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.1.0+54

environment:
  sdk: ">=3.3.3 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  flutter_bloc: ^8.1.5
  dio: ^5.4.3+1
  connectivity_plus: ^6.1.0
  package_info_plus: ^8.0.0
  flutter_svg: ^2.0.10+1
  google_fonts: ^6.2.1
  gap: ^3.0.1
  equatable: ^2.0.5
  shared_preferences: ^2.2.3
  bot_toast: ^4.1.3
  permission_handler: ^11.3.1
  image_picker: ^1.1.2
  google_maps_flutter: ^2.2.8
  firebase_core: ^3.6.0
  firebase_analytics: ^11.3.3
  firebase_messaging: ^15.1.3
  http: ^1.2.1
  flutter_typeahead: ^5.2.0
  pinput: ^4.0.0
  carousel_slider: ^5.0.0
  flutter_local_notifications: ^17.1.2
  dotted_border: ^2.1.0
  device_info_plus: ^9.0.2
  url_launcher: ^6.2.6
  intl: ^0.19.0
  image_cropper: ^8.0.2
  numberpicker: ^2.1.2
  loading_animation_widget: ^1.2.1
  stream_transform: ^2.1.0
  percent_indicator: ^4.2.3
  path_provider: ^2.1.3
  cached_network_image: ^3.3.1
  pdf: ^3.11.0
  syncfusion_flutter_datepicker: ^26.1.40
  fl_chart: ^0.68.0
  screenshot: ^3.0.0
  share_plus: ^10.0.2
  printing: ^5.13.1
  excel: ^2.0.0-null-safety-3
  open_filex: ^4.4.0
  get: ^4.6.6
  geolocator: ^12.0.0
  geocoding: ^3.0.0
  syncfusion_flutter_pdfviewer: ^26.1.41
  app_settings: ^6.1.1
  wolt_modal_sheet: ^0.9.3
  flutter_radar_chart: ^0.2.1
  mixpanel_flutter: ^2.3.3
  qr_flutter: ^4.1.0


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/images/png/
    - assets/images/svg/
    - assets/images/jpg/
  fonts:
    - family: Poppins-Black
      fonts:
        - asset: assets/font/Poppins-Black.ttf
    - family: Poppins-ExtraBold
      fonts:
        - asset: assets/font/Poppins-ExtraBold.ttf
    - family: Poppins-Medium
      fonts:
        - asset: assets/font/Poppins-Medium.ttf
    - family: Poppins
      fonts:
        - asset: assets/font/Poppins-Regular.ttf
    - family: Poppins-SemiBold
      fonts:
        - asset: assets/font/Poppins-SemiBold.ttf
    - family: Montserrat-Regular
      fonts:
        - asset: assets/font/Montserrat-Regular.ttf
    - family: Montserrat-Bold
      fonts:
        - asset: assets/font/Montserrat-Bold.ttf
    - family: Montserrat-SemiBold
      fonts:
        - asset: assets/font/Montserrat-SemiBold.ttf

  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
